package router

import (
	dashboard "git.uozi.org/uozi/potato-billing-api/api/admin"
	"git.uozi.org/uozi/potato-billing-api/api/admin/settings"
	"git.uozi.org/uozi/potato-billing-api/api/admin/user"
	"git.uozi.org/uozi/potato-billing-api/api/billing"
	"github.com/uozi-tech/cosy"
)

func initAdminRouter() {
	r := cosy.GetEngine()
	admin := r.Group("/admin/", AuthRequired())
	{
		user.InitUserRouter(admin)
		settings.InitRouter(admin)
		billing.RegisterRoutes(admin)
		dashboard.InitDashboardRouter(admin)
	}
}

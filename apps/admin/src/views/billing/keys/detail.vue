<script setup lang="ts">
import type { KeyStatusResponse, UsageLog, UsageStats } from '@/api/key'
import type { CreateQuotaPackageRequest, QuotaPackage } from '@/api/quota'
import { Alert, AlertDescription, Badge, Button, Card, CardContent, CardHeader, CardTitle, Dialog, DialogContent, DialogHeader, DialogTitle, Input, Label, Progress, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Switch, Textarea } from '@billing/ui'
import { AlertCircle, BarChart3, CheckCircle, CreditCard, Gift, History, Info, Package, Plus, Shield, User, Wallet, XCircle } from 'lucide-vue-next'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { keyApi } from '@/api/key'
import { quotaApi } from '@/api/quota'

const route = useRoute()
const keyValue = ref(route.params.key as string)

// 数据状态
const loading = ref(false)
const keyInfo = ref<KeyStatusResponse | null>(null)
const usageStats = ref<UsageStats | null>(null)
const usageHistory = ref<UsageLog[]>([])
const selectedPeriod = ref<'day' | 'week' | 'month'>('month')

// 配额编辑弹窗
const quotaEditDialog = ref(false)
const editingQuota = ref<QuotaPackage | null>(null)
const quotaForm = ref({
  module: 'llm' as 'llm' | 'tts' | 'asr',
  quota: 0,
  expires_at: 0,
  model_name: '',
  description: '',
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalPages = ref(0)

// 计算属性：计费方式描述
const billingModeDescription = computed(() => {
  if (!keyInfo.value)
    return ''

  const hasQuotas = keyInfo.value.quotas && keyInfo.value.quotas.length > 0
  const hasBalance = keyInfo.value.user && keyInfo.value.user.balance > 0

  if (hasQuotas && hasBalance) {
    return '混合计费：优先使用资源包，资源包用完后扣除用户余额'
  }
  else if (hasQuotas) {
    return '资源包计费：仅使用配置的资源包'
  }
  else if (hasBalance) {
    return '余额计费：直接扣除用户余额'
  }
  else {
    return '无可用计费方式：请添加资源包或为用户充值'
  }
})

// 计算属性：详细配额统计，区分具体模型
const quotaStats = computed(() => {
  if (!keyInfo.value?.quotas)
    return null

  const detailedStats: Array<{
    key: string
    module: string
    modelName: string
    displayName: string
    total: number
    used: number
    available: number
    quotas: any[]
  }> = []

  // 按模块和模型分组
  const groupedQuotas = new Map<string, any[]>()

  keyInfo.value.quotas.forEach((quota) => {
    // 创建唯一key：模块+模型名（如果有的话）
    const modelKey = quota.model_name || 'general'
    const groupKey = `${quota.module}-${modelKey}`

    if (!groupedQuotas.has(groupKey)) {
      groupedQuotas.set(groupKey, [])
    }
    groupedQuotas.get(groupKey)!.push(quota)
  })

  // 生成统计数据
  groupedQuotas.forEach((quotas, groupKey) => {
    const [module, modelKey] = groupKey.split('-')
    const modelName = modelKey === 'general' ? '' : modelKey

    const total = quotas.reduce((sum, q) => sum + q.quota, 0)
    const used = quotas.reduce((sum, q) => sum + q.used, 0)
    const available = quotas.reduce((sum, q) => sum + q.available, 0)

    // 生成显示名称
    let displayName = getModuleName(module)
    if (modelName) {
      displayName += ` (${modelName})`
    }
    else {
      displayName += ' (通用)'
    }

    detailedStats.push({
      key: groupKey,
      module,
      modelName,
      displayName,
      total,
      used,
      available,
      quotas,
    })
  })

  // 按模块类型和模型名称排序
  detailedStats.sort((a, b) => {
    if (a.module !== b.module) {
      return a.module.localeCompare(b.module)
    }
    // 通用模型排在前面
    if (!a.modelName && b.modelName)
      return -1
    if (a.modelName && !b.modelName)
      return 1
    return a.modelName.localeCompare(b.modelName)
  })

  return detailedStats
})

// 获取Key状态信息
async function fetchKeyInfo() {
  if (!keyValue.value)
    return

  loading.value = true
  try {
    keyInfo.value = await keyApi.getKeyStatus(keyValue.value)
  }
  catch (error) {
    console.error('获取Key信息失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 获取用量统计
async function fetchUsageStats() {
  if (!keyValue.value)
    return

  try {
    usageStats.value = await keyApi.getUsageStats(keyValue.value, selectedPeriod.value)
  }
  catch (error) {
    console.error('获取用量统计失败:', error)
  }
}

// 获取用量历史
async function fetchUsageHistory() {
  if (!keyValue.value)
    return

  try {
    const result = await keyApi.getUsageHistory(keyValue.value, {
      page: currentPage.value,
      page_size: pageSize.value,
    })
    usageHistory.value = result.data
    totalPages.value = result.pagination.total_pages
  }
  catch (error) {
    console.error('获取用量历史失败:', error)
  }
}

// 新增配额
function addNewQuota() {
  editingQuota.value = null
  quotaForm.value = {
    module: 'llm',
    quota: 1000000,
    expires_at: 0,
    model_name: '',
    description: '',
  }
  quotaEditDialog.value = true
}

// 更新配额
async function updateQuota() {
  if (!keyValue.value)
    return

  try {
    // 创建新配额 - 需要包含用户ID和API Key信息
    if (!keyInfo.value?.user?.id) {
      console.error('无法获取用户信息')
      return
    }

    const createData: CreateQuotaPackageRequest = {
      user_id: keyInfo.value.user.id,
      api_key: keyValue.value,
      module: quotaForm.value.module,
      quota: quotaForm.value.quota,
      expires_at: quotaForm.value.expires_at || undefined,
      model_name: quotaForm.value.model_name || undefined,
      description: quotaForm.value.description,
      type: 'admin', // 管理员创建的配额
    }

    console.log(createData)

    await quotaApi.createItem(createData)

    quotaEditDialog.value = false
    await fetchKeyInfo() // 重新获取数据
  }
  catch (error) {
    console.error('操作配额失败:', error)
  }
}

// 格式化时间
function formatTime(timestamp: number) {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 格式化金额
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 4,
  }).format(amount)
}

// 获取模块名称
function getModuleName(module: string) {
  const moduleNames = {
    llm: 'LLM',
    tts: 'TTS语音合成',
    asr: 'ASR语音识别',
  }
  return moduleNames[module as keyof typeof moduleNames] || module
}

// 获取状态颜色
function getStatusColor(status: string) {
  return status === 'ok' ? 'default' : 'destructive'
}

// 获取可用性颜色
function getAvailabilityColor(available: boolean) {
  return available ? 'default' : 'destructive'
}

// 计算使用率
function getUsageRate(quota: QuotaPackage) {
  if (quota.quota === 0)
    return 0
  return Math.round((quota.used / quota.quota) * 100)
}

// 获取计费类型颜色
function getBillingTypeColor(type: string) {
  return type === 'quota' ? 'default' : 'secondary'
}

// 获取配额来源信息
function getQuotaSource(quota: QuotaPackage) {
  const sources = {
    admin: { label: '管理员创建', icon: Shield, color: 'destructive' as const },
    purchase: { label: '用户购买', icon: CreditCard, color: 'default' as const },
    gift: { label: '系统赠送', icon: Gift, color: 'secondary' as const },
    promotion: { label: '活动赠送', icon: Gift, color: 'secondary' as const },
  }
  return sources[quota.type as keyof typeof sources] || sources.admin
}

// 切换配额状态
async function toggleQuotaStatus(quota: QuotaPackage) {
  try {
    if (quota.status === 'active') {
      await quotaApi.disableItem(quota.id)
    }
    else {
      await quotaApi.enableItem(quota.id)
    }
    await fetchKeyInfo() // 重新获取数据
  }
  catch (error) {
    console.error('切换配额状态失败:', error)
  }
}

// 检查配额是否过期
function isQuotaExpired(quota: QuotaPackage) {
  if (!quota.expires_at)
    return false
  return new Date(quota.expires_at).getTime() < Date.now()
}

// 格式化过期时间
function formatExpiryTime(timestamp?: number) {
  if (!timestamp)
    return '永不过期'
  const date = new Date(timestamp)
  const now = new Date()
  if (date.getTime() < now.getTime()) {
    return '已过期'
  }
  return date.toLocaleString('zh-CN')
}

// 格式化不同单位的使用量
function formatUsageWithUnit(usage: number, unit: string) {
  let unitStr = unit
  switch (unit) {
    case 'token':
      unitStr = 'tokens'
      break
    case 'character':
      unitStr = '字符'
      break
    case 'seconds':
      unitStr = '秒'
  }
  let usageStr = usage.toLocaleString()
  if (unit === 'token' || unit === 'character') {
    // 对于tokens和character，使用K/M格式
    if (usage >= 1000000) {
      usageStr = `${(usage / 1000000).toFixed(1)}M`
    }
    if (usage >= 1000) {
      usageStr = `${(usage / 1000).toFixed(1)}K`
    }
    usageStr = `${usageStr} ${unitStr}`
  }
  else if (unit === 'second') {
    // 对于秒数，格式化为时分秒
    if (usage >= 3600) {
      const hours = Math.floor(usage / 3600)
      const minutes = Math.floor((usage % 3600) / 60)
      const seconds = usage % 60
      usageStr = `${hours}小时${minutes}分${seconds}秒`
    }
    else if (usage >= 60) {
      const minutes = Math.floor(usage / 60)
      const seconds = usage % 60
      usageStr = `${minutes}分${seconds}秒`
    }
    else {
      usageStr = `${usageStr}s`
    }
  }
  return usageStr
}

// 获取模块颜色样式类
function getModuleColorClass(module: string, isBackground = false) {
  const colorMap = {
    llm: isBackground ? 'bg-blue-500' : 'bg-blue-500',
    tts: isBackground ? 'bg-green-500' : 'bg-green-500',
    asr: isBackground ? 'bg-purple-500' : 'bg-purple-500',
  }
  return colorMap[module as keyof typeof colorMap] || (isBackground ? 'bg-gray-500' : 'bg-gray-500')
}

// 监听周期变化
watch(selectedPeriod, fetchUsageStats)

// 监听分页变化
watch([currentPage], fetchUsageHistory)

// 初始化数据
onMounted(async () => {
  await Promise.all([
    fetchKeyInfo(),
    fetchUsageStats(),
    fetchUsageHistory(),
  ])
})
</script>

<template>
  <div class="space-y-6">
    <!-- 顶部导航 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <!-- <Button
          variant="ghost"
          @click="goBack"
        >
          <ArrowLeft class="w-4 h-4" />
          返回
        </Button> -->
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            API Key 详情
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            {{ keyValue }}
          </p>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="flex justify-center py-8"
    >
      <div class="text-gray-500">
        加载中...
      </div>
    </div>

    <template v-else-if="keyInfo">
      <!-- 计费方式说明 -->
      <Alert>
        <Info class="w-4 h-4" />
        <AlertDescription>
          <strong>计费说明：</strong>{{ billingModeDescription }}
        </AlertDescription>
      </Alert>

      <!-- Key基本信息 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <User class="w-5 h-5" />
            Key信息
          </CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <div class="text-sm font-medium text-gray-500">
                状态
              </div>
              <Badge
                :variant="getStatusColor(keyInfo.status)"
                class="mt-1"
              >
                <CheckCircle
                  v-if="keyInfo.status === 'ok'"
                  class="w-3 h-3"
                />
                <XCircle
                  v-else
                  class="w-3 h-3"
                />
                {{ keyInfo.status === 'ok' ? '正常' : '阻止' }}
              </Badge>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-500">
                可用性
              </div>
              <Badge
                :variant="getAvailabilityColor(keyInfo.available)"
                class="mt-1"
              >
                <CheckCircle
                  v-if="keyInfo.available"
                  class="w-3 h-3"
                />
                <AlertCircle
                  v-else
                  class="w-3 h-3"
                />
                {{ keyInfo.available ? '可用' : '不可用' }}
              </Badge>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-500">
                创建时间
              </div>
              <div class="mt-1 text-sm">
                {{ formatTime(keyInfo.created_at) }}
              </div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-500">
                更新时间
              </div>
              <div class="mt-1 text-sm">
                {{ formatTime(keyInfo.updated_at) }}
              </div>
            </div>
          </div>

          <!-- 关联用户信息 -->
          <div
            v-if="keyInfo.user"
            class="border-t pt-4"
          >
            <div class="text-sm font-medium text-gray-500 mb-2">
              关联用户信息
            </div>
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                <User class="w-5 h-5 text-gray-500" />
              </div>
              <div class="flex-1">
                <div class="font-medium">
                  {{ keyInfo.user.name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ keyInfo.user.email }}
                </div>
                <div class="flex items-center gap-4 mt-2">
                  <div class="flex items-center gap-1">
                    <Wallet class="w-4 h-4 text-green-600" />
                    <span class="text-sm font-medium text-green-600">
                      余额: {{ formatCurrency(keyInfo.user.balance || 0) }}
                    </span>
                  </div>
                  <div class="flex items-center gap-1">
                    <Package class="w-4 h-4 text-blue-600" />
                    <span class="text-sm text-blue-600">
                      {{ quotaStats ? quotaStats.length : keyInfo.quotas.length }} 个资源组
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 资源包概览 -->
          <div
            v-if="quotaStats && quotaStats.length > 0"
            class="border-t pt-4"
          >
            <div class="text-sm font-medium text-gray-500 mb-3">
              资源包概览
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="stat in quotaStats"
                :key="stat.key"
                class="p-3 bg-gray-50 rounded-lg"
              >
                <div class="text-sm font-medium mb-2">
                  {{ stat.displayName }}
                </div>
                <div class="space-y-1">
                  <div class="flex justify-between text-xs">
                    <span>总配额</span>
                    <span>{{ stat.total.toLocaleString() }}</span>
                  </div>
                  <div class="flex justify-between text-xs">
                    <span>已使用</span>
                    <span>{{ stat.used.toLocaleString() }}</span>
                  </div>
                  <div class="flex justify-between text-xs">
                    <span>可用量</span>
                    <span>{{ stat.available.toLocaleString() }}</span>
                  </div>
                  <Progress
                    :model-value="stat.total > 0 ? (stat.used / stat.total) * 100 : 0"
                    class="h-1 mt-2"
                  />
                  <div class="flex justify-between text-xs text-gray-500 mt-1">
                    <span>使用率</span>
                    <span>{{ stat.total > 0 ? Math.round((stat.used / stat.total) * 100) : 0 }}%</span>
                  </div>
                  <div
                    v-if="stat.quotas.length > 1"
                    class="text-xs text-blue-600 mt-1"
                  >
                    {{ stat.quotas.length }} 个资源包
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 资源包管理 -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle class="flex items-center gap-2">
              <BarChart3 class="w-5 h-5" />
              资源包管理
            </CardTitle>
            <Button
              size="sm"
              @click="addNewQuota"
            >
              <Plus class="w-4 h-4" />
              添加资源包
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div
              v-if="keyInfo.quotas.length === 0"
              class="text-center py-8 text-gray-500"
            >
              <Package class="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>暂无资源包配置</p>
              <p class="text-sm">
                点击上方"添加资源包"按钮为此Key配置资源包
              </p>
              <p class="text-sm text-blue-600 mt-2">
                支持为不同模型配置独立的资源包
              </p>
            </div>
            <div
              v-else
              class="grid gap-4"
            >
              <div
                v-for="quota in keyInfo.quotas"
                :key="quota.id"
                class="border rounded-lg p-4"
                :class="{ 'opacity-50': quota.status !== 'active' || isQuotaExpired(quota) || quota.available === 0 }"
              >
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center gap-2 flex-wrap">
                    <Badge variant="outline">
                      {{ getModuleName(quota.module) }}
                    </Badge>
                    <Badge
                      v-if="quota.model_name"
                      variant="secondary"
                      class="text-xs"
                    >
                      {{ quota.model_name }}
                    </Badge>
                    <Badge
                      v-else
                      variant="outline"
                      class="text-xs"
                    >
                      通用
                    </Badge>
                    <!-- 配额来源 -->
                    <Badge
                      :variant="getQuotaSource(quota).color"
                      class="text-xs"
                    >
                      <component
                        :is="getQuotaSource(quota).icon"
                        class="w-3 h-3"
                      />
                      {{ getQuotaSource(quota).label }}
                    </Badge>
                    <!-- 状态标识 -->
                    <Badge
                      v-if="isQuotaExpired(quota)"
                      variant="destructive"
                      class="text-xs"
                    >
                      已过期
                    </Badge>
                    <Badge
                      v-else-if="quota.status !== 'active'"
                      variant="secondary"
                      class="text-xs"
                    >
                      已禁用
                    </Badge>
                  </div>
                  <!-- 启用/禁用开关 -->
                  <div class="flex items-center gap-2">
                    <span class="text-xs text-gray-500">
                      {{ quota.status === 'active' ? '启用' : '禁用' }}
                    </span>
                    <Switch
                      :model-value="quota.status === 'active'"
                      :disabled="isQuotaExpired(quota) || quota.available === 0"
                      @update:model-value="toggleQuotaStatus(quota)"
                    />
                  </div>
                </div>

                <!-- 配额详情信息 -->
                <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
                  <div>
                    <span class="text-gray-500">过期时间:</span>
                    <span
                      class="ml-2"
                      :class="{ 'text-red-600': isQuotaExpired(quota) }"
                    >
                      {{ formatExpiryTime(quota.expires_at) }}
                    </span>
                  </div>
                  <div v-if="quota.operator">
                    <span class="text-gray-500">操作人:</span>
                    <span class="ml-2">{{ quota.operator.name }}</span>
                  </div>
                </div>

                <div class="space-y-2">
                  <div class="flex justify-between text-sm">
                    <span>已用量 / 总配额</span>
                    <span>{{ quota.used.toLocaleString() }} / {{ quota.quota.toLocaleString() }}</span>
                  </div>
                  <Progress
                    :model-value="getUsageRate(quota)"
                    class="h-2"
                  />
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>使用率: {{ getUsageRate(quota) }}%</span>
                    <span>剩余: {{ quota.available.toLocaleString() }}</span>
                  </div>
                </div>

                <!-- 备注信息 -->
                <div
                  v-if="quota.description"
                  class="mt-3 p-2 bg-gray-50 rounded text-sm text-gray-600"
                >
                  <div class="text-gray-500 text-xs mb-1">
                    备注:
                  </div>
                  {{ quota.description }}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 用量统计 -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle class="flex items-center gap-2">
              <BarChart3 class="w-5 h-5" />
              用量统计
            </CardTitle>
            <Select v-model="selectedPeriod">
              <SelectTrigger class="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">
                  今日
                </SelectItem>
                <SelectItem value="week">
                  本周
                </SelectItem>
                <SelectItem value="month">
                  本月
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div
            v-if="usageStats"
            class="space-y-4"
          >
            <!-- 总计 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">
                  {{ formatCurrency(usageStats.total_cost) }}
                </div>
                <div class="text-sm text-green-600">
                  总费用
                </div>
              </div>
              <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">
                  {{ usageStats.total_count.toLocaleString() }}
                </div>
                <div class="text-sm text-purple-600">
                  调用次数
                </div>
              </div>
            </div>

            <!-- 按模块详细统计 -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h4 class="font-medium">
                  按模块详细统计
                </h4>
                <Badge
                  variant="secondary"
                  class="text-xs"
                >
                  {{ selectedPeriod === 'day' ? '今日' : selectedPeriod === 'week' ? '本周' : '本月' }}
                </Badge>
              </div>

              <div
                v-if="usageStats.by_module.length === 0"
                class="text-center py-8 text-gray-500"
              >
                <Package class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p class="font-medium">
                  暂无使用记录
                </p>
                <p class="text-sm">
                  当前时间段内该Key暂无使用记录
                </p>
              </div>

              <!-- 模块统计卡片网格 -->
              <div
                v-else
                class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
              >
                <div
                  v-for="moduleStats in usageStats.by_module"
                  :key="moduleStats.module"
                  class="bg-white border rounded-lg p-6 hover:shadow-md transition-shadow"
                >
                  <!-- 模块头部 -->
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-2">
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="getModuleColorClass(moduleStats.module)"
                      />
                      <Badge
                        variant="outline"
                        class="text-sm"
                      >
                        {{ getModuleName(moduleStats.module) }}
                      </Badge>
                    </div>
                    <Badge
                      :variant="moduleStats.count > 0 ? 'default' : 'secondary'"
                      class="text-xs"
                    >
                      {{ moduleStats.count > 0 ? '活跃' : '无使用' }}
                    </Badge>
                  </div>

                  <!-- 使用量统计 -->
                  <div class="space-y-3">
                    <div>
                      <div class="flex items-center justify-between text-sm mb-1">
                        <span class="text-gray-600">使用量</span>
                        <span class="font-medium">{{ formatUsageWithUnit(moduleStats.total_usage, moduleStats.unit) }}</span>
                      </div>
                    </div>

                    <div>
                      <div class="flex items-center justify-between text-sm mb-1">
                        <span class="text-gray-600">调用次数</span>
                        <span class="font-medium">{{ moduleStats.count.toLocaleString() }}</span>
                      </div>
                    </div>

                    <div>
                      <div class="flex items-center justify-between text-sm mb-1">
                        <span class="text-gray-600">总费用</span>
                        <span class="font-medium text-green-600">{{ formatCurrency(moduleStats.total_cost) }}</span>
                      </div>
                    </div>

                    <!-- 平均每次调用成本 -->
                    <div v-if="moduleStats.count > 0">
                      <div class="flex items-center justify-between text-sm mb-1">
                        <span class="text-gray-600">单次成本</span>
                        <span class="font-medium text-blue-600">
                          {{ formatCurrency(moduleStats.total_cost / moduleStats.count) }}
                        </span>
                      </div>
                    </div>

                    <!-- 使用效率指标 -->
                    <div
                      v-if="moduleStats.count > 0"
                      class="pt-2 border-t"
                    >
                      <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>平均每次用量</span>
                        <span>{{ formatUsageWithUnit(Math.round(moduleStats.total_usage / moduleStats.count), moduleStats.unit) }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 在总统计中的占比 -->
                  <div class="mt-4 pt-3 border-t">
                    <div class="text-xs text-gray-500 mb-2">
                      在总费用中占比
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          class="h-2 rounded-full transition-all duration-300"
                          :class="getModuleColorClass(moduleStats.module, true)"
                          :style="{ width: `${Math.min((moduleStats.total_cost / (usageStats.total_cost || 1)) * 100, 100)}%` }"
                        />
                      </div>
                      <span class="text-xs font-medium text-gray-600">
                        {{ Math.round((moduleStats.total_cost / (usageStats.total_cost || 1)) * 100) }}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-else
            class="text-center py-8 text-gray-500"
          >
            暂无统计数据
          </div>
        </CardContent>
      </Card>

      <!-- 使用历史 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <History class="w-5 h-5" />
            使用历史
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            v-if="usageHistory.length === 0"
            class="text-center py-8 text-gray-500"
          >
            暂无使用记录
          </div>
          <div
            v-else
            class="space-y-3"
          >
            <div
              v-for="log in usageHistory"
              :key="log.id"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div class="flex items-center gap-3">
                <Badge variant="outline">
                  {{ getModuleName(log.module) }}
                </Badge>
                <Badge
                  :variant="getBillingTypeColor(log.billing_type)"
                  class="text-xs"
                >
                  {{ log.billing_type === 'quota' ? '资源包' : '余额' }}
                </Badge>
                <div>
                  <div class="font-medium text-sm">
                    {{ log.model_name || '默认模型' }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ formatTime(log.created_at) }}
                  </div>
                </div>
              </div>
              <div class="text-right">
                <div class="font-medium">
                  {{ formatUsageWithUnit(log.usage, log.unit) }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ formatCurrency(log.cost) }}
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <div
              v-if="totalPages > 1"
              class="flex justify-center pt-4"
            >
              <div class="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  :disabled="currentPage <= 1"
                  @click="currentPage--"
                >
                  上一页
                </Button>
                <span class="text-sm text-gray-500">
                  第 {{ currentPage }} 页，共 {{ totalPages }} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  :disabled="currentPage >= totalPages"
                  @click="currentPage++"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </template>

    <!-- 配额编辑弹窗 -->
    <Dialog v-model:open="quotaEditDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{{ editingQuota ? '编辑配额' : '添加配额' }}</DialogTitle>
        </DialogHeader>
        <div class="space-y-4">
          <div>
            <Label>模块</Label>
            <Select
              v-model="quotaForm.module"
              :disabled="!!editingQuota"
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="llm">
                  LLM
                </SelectItem>
                <SelectItem value="tts">
                  TTS语音合成
                </SelectItem>
                <SelectItem value="asr">
                  ASR语音识别
                </SelectItem>
              </SelectContent>
            </Select>
            <div
              v-if="editingQuota"
              class="text-xs text-gray-500 mt-1"
            >
              编辑模式下不能修改模块类型
            </div>
          </div>

          <div>
            <Label>配额</Label>
            <Input
              v-model.number="quotaForm.quota"
              type="number"
              min="0"
              placeholder="输入配额数量"
            />
          </div>

          <div>
            <Label>模型名称（可选）</Label>
            <Input
              v-model="quotaForm.model_name"
              :disabled="!!editingQuota"
              placeholder="留空表示通用模型"
            />
            <div
              v-if="editingQuota"
              class="text-xs text-gray-500 mt-1"
            >
              编辑模式下不能修改模型名称
            </div>
          </div>

          <div>
            <Label>过期时间</Label>
            <Input
              v-model="quotaForm.expires_at"
              type="datetime-local"
              placeholder="选择过期时间"
            />
          </div>

          <div>
            <Label>描述</Label>
            <Textarea
              v-model="quotaForm.description"
              placeholder="输入配额描述"
            />
          </div>

          <div class="flex justify-end gap-2">
            <Button
              variant="outline"
              @click="quotaEditDialog = false"
            >
              取消
            </Button>
            <Button @click="updateQuota">
              {{ editingQuota ? '保存' : '添加' }}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

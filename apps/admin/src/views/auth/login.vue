<script setup lang="ts">
import { Button, Checkbox, Input, Label } from '@billing/ui'
import { AlertCircle, Loader2 } from 'lucide-vue-next'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { authApi } from '@/api/auth'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

const form = reactive({
  email: '',
  password: '',
  remember: false,
})

const loading = ref(false)
const error = ref('')

async function handleLogin() {
  if (!form.email || !form.password)
    return

  loading.value = true
  error.value = ''

  try {
    const response = await authApi.login({
      email: form.email,
      password: form.password,
    })

    // 假设返回的数据包含token和user信息
    if (response.token) {
      userStore.setToken(response.token)

      // 如果选择记住登录状态，保存到localStorage
      if (form.remember) {
        localStorage.setItem('auth_token', response.token)
      }

      // 跳转到主页
      await router.push('/dashboard')
    }
  }
  catch (err: any) {
    console.error('登录失败:', err)
    error.value = err.message || '登录失败，请检查邮箱和密码'
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          登录计费系统管理端
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          请使用您的管理员账户登录
        </p>
      </div>

      <form
        class="mt-8 space-y-6"
        @submit.prevent="handleLogin"
      >
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <Label
              for="email"
              class="sr-only"
            >邮箱地址</Label>
            <Input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="relative block w-full rounded-t-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              placeholder="邮箱地址"
              :disabled="loading"
            />
          </div>
          <div>
            <Label
              for="password"
              class="sr-only"
            >密码</Label>
            <Input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="current-password"
              required
              class="relative block w-full rounded-b-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              placeholder="密码"
              :disabled="loading"
            />
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <Checkbox
              id="remember-me"
              v-model="form.remember"
              :disabled="loading"
            />
            <Label
              for="remember-me"
              class="ml-2 block text-sm text-gray-900"
            >
              记住登录状态
            </Label>
          </div>
        </div>

        <!-- 错误提示 -->
        <div
          v-if="error"
          class="rounded-md bg-red-50 p-4"
        >
          <div class="flex">
            <AlertCircle class="h-5 w-5 text-red-400" />
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                登录失败
              </h3>
              <div class="mt-2 text-sm text-red-700">
                {{ error }}
              </div>
            </div>
          </div>
        </div>

        <div>
          <Button
            type="submit"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            :disabled="loading || !form.email || !form.password"
          >
            <Loader2
              v-if="loading"
              class="w-4 h-4 mr-2 animate-spin"
            />
            {{ loading ? '登录中...' : '登录' }}
          </Button>
        </div>
      </form>
    </div>
  </div>
</template>

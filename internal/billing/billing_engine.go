package billing

import (
	"context"
	"errors"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// BillingEngine 计费引擎 - 负责协调各服务完成计费流程
type BillingEngine struct {
	pricingService *PricingService
	quotaService   *QuotaService
	keyService     *KeyService
}

// NewBillingEngine 创建计费引擎
func NewBillingEngine(pricingService *PricingService, quotaService *QuotaService, keyService *KeyService) *BillingEngine {
	return &BillingEngine{
		pricingService: pricingService,
		quotaService:   quotaService,
		keyService:     keyService,
	}
}

// ProcessBilling 处理计费请求 - 协调各服务完成计费流程
func (be *BillingEngine) ProcessBilling(req types.BillingRequest) (*types.BillingResult, error) {
	return be.processBillingInTransaction(req)
}

func (be *BillingEngine) processBillingInTransaction(req types.BillingRequest) (*types.BillingResult, error) {
	var result *types.BillingResult

	err := query.Use(cosy.UseDB(context.Background())).Transaction(func(tx *query.Query) error {
		// 1. 验证API Key（委托给KeyService处理）
		billingKey, err := be.validateAPIKey(tx, req.APIKey)
		if err != nil {
			result = &types.BillingResult{Success: false, Message: err.Error()}
			return err
		}

		// 2. 获取价格规则（委托给PricingService）
		pricingRule, err := be.pricingService.FindEffectivePricingRule(req.Module, req.ModelName)
		if err != nil {
			result = &types.BillingResult{Success: false, Message: "未找到价格规则"}
			return err
		}

		// 使用PricingRule的CalculateCost方法计算费用，支持基数单位
		cost := pricingRule.CalculateCost(req.Usage)

		// 3. 尝试资源包计费（委托给QuotaService）
		quotaResult := be.quotaService.TryQuotaBilling(tx, billingKey.UserID, req.APIKey, req.Module, req.ModelName, req.Usage)
		if quotaResult.Success {
			// 记录用量日志
			err = be.createUsageLog(tx, req, cost, pricingRule, types.BillingTypeQuota, quotaResult.QuotaPackageID, billingKey.UserID)
			if err != nil {
				result = &types.BillingResult{Success: false, Message: "记录用量日志失败"}
				return err
			}

			result = &types.BillingResult{
				Success:        true,
				Cost:           cost,
				BillingType:    types.BillingTypeQuota,
				QuotaPackageID: quotaResult.QuotaPackageID,
				PricingRuleID:  pricingRule.ID,
				Message:        "资源包计费成功",
			}
			return nil
		}

		logger.Debugf("资源包不足，尝试余额计费(api_key: %s, module: %s, model_name: %s, usage: %d, cost: %f)", req.APIKey, req.Module, req.ModelName, req.Usage, cost)

		// 4. 资源包不足，尝试余额计费（委托给UserService处理）
		if billingKey.User == nil {
			result = &types.BillingResult{Success: false, Message: "用户信息不存在"}
			return errors.New("用户信息不存在")
		}

		err = be.processBalanceBilling(tx, billingKey.User, cost)
		if err != nil {
			result = &types.BillingResult{Success: false, Message: err.Error()}
			return err
		}

		// 记录用量日志
		err = be.createUsageLog(tx, req, cost, pricingRule, types.BillingTypeBalance, 0, billingKey.UserID)
		if err != nil {
			result = &types.BillingResult{Success: false, Message: "记录用量日志失败"}
			return err
		}

		result = &types.BillingResult{
			Success:       true,
			Cost:          cost,
			BillingType:   types.BillingTypeBalance,
			PricingRuleID: pricingRule.ID,
			Message:       "余额计费成功",
		}
		return nil
	})

	return result, err
}

// validateAPIKey 验证API Key
func (be *BillingEngine) validateAPIKey(tx *query.Query, apiKey string) (*model.ApiKey, error) {
	billingKey, err := tx.ApiKey.
		Preload(tx.ApiKey.User).
		Where(tx.ApiKey.APIKey.Eq(apiKey)).
		First()
	if err != nil {
		return nil, errors.New("API Key不存在")
	}

	if billingKey.Status != types.KeyStatusOk {
		return nil, errors.New("API Key已被禁用")
	}

	return billingKey, nil
}

// processBalanceBilling 处理余额计费
func (be *BillingEngine) processBalanceBilling(tx *query.Query, user *model.User, cost float64) error {
	if !user.HasSufficientBalance(cost) {
		return errors.New("余额不足且无可用资源包")
	}

	// 扣减余额
	err := user.SubtractBalance(cost)
	if err != nil {
		return errors.New("扣减余额失败")
	}

	return nil
}

// createUsageLog 创建用量日志
func (be *BillingEngine) createUsageLog(tx *query.Query, req types.BillingRequest, cost float64, pricingRule *model.PricingRule, billingType string, quotaPackageID, userID uint64) error {
	usageLog := &model.UsageLog{
		APIKey:         req.APIKey,
		Module:         req.Module,
		ModelName:      req.ModelName,
		Usage:          req.Usage,
		UnitPrice:      pricingRule.GetEffectiveUnitPrice(), // 使用实际单价（考虑基数单位）
		Cost:           cost,
		Currency:       pricingRule.Currency,
		Unit:           pricingRule.GetUnitDisplay(), // 使用动态生成的显示单位名称
		PricingRuleID:  pricingRule.ID,
		BillingType:    billingType,
		QuotaPackageID: quotaPackageID,
		UserID:         userID,
		Metadata:       req.Metadata,
	}

	return tx.UsageLog.Create(usageLog)
}

// GetBillingHistory 获取计费历史
func (be *BillingEngine) GetBillingHistory(ctx context.Context, apiKey string, limit, offset int) ([]model.UsageLog, int64, error) {
	db := cosy.UseDB(ctx)

	var logs []model.UsageLog
	var total int64

	// 获取总数
	err := db.Model(&model.UsageLog{}).
		Where("api_key = ?", apiKey).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = db.Where("api_key = ?", apiKey).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&logs).Error

	return logs, total, err
}

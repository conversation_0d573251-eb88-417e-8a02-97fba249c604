package billing

import (
	"context"
	"fmt"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// StatService 统计服务
type StatService struct {
	cacheService *CacheService
	config       *settings.BillingConfig
}

// NewStatService 创建统计服务
func NewStatService(cacheService *CacheService, config *settings.BillingConfig) *StatService {
	return &StatService{
		cacheService: cacheService,
		config:       config,
	}
}

// DashboardStats Dashboard统计数据结构
type DashboardStats struct {
	// 核心指标
	TotalRevenue float64 `json:"total_revenue"` // 总收入
	MonthRevenue float64 `json:"month_revenue"` // 本月收入
	TodayRevenue float64 `json:"today_revenue"` // 今日收入
	TotalUsers   int64   `json:"total_users"`   // 总用户数
	ActiveUsers  int64   `json:"active_users"`  // 活跃用户数
	TotalKeys    int64   `json:"total_keys"`    // 总Key数
	ActiveKeys   int64   `json:"active_keys"`   // 活跃Key数
	BlockedKeys  int64   `json:"blocked_keys"`  // 阻止Key数

	// 计费相关
	TotalQuotaPackages  int64   `json:"total_quota_packages"`  // 总资源包数
	ActiveQuotaPackages int64   `json:"active_quota_packages"` // 活跃资源包数
	TotalBalance        float64 `json:"total_balance"`         // 用户总余额
	AvgUserBalance      float64 `json:"avg_user_balance"`      // 平均用户余额

	// 使用量统计
	TotalUsage    int64 `json:"total_usage"`     // 总使用量
	MonthUsage    int64 `json:"month_usage"`     // 本月使用量
	TodayUsage    int64 `json:"today_usage"`     // 今日使用量
	AvgDailyUsage int64 `json:"avg_daily_usage"` // 平均日使用量

	// 趋势数据
	RevenueGrowth float64 `json:"revenue_growth"` // 收入增长率
	UserGrowth    float64 `json:"user_growth"`    // 用户增长率
	UsageGrowth   float64 `json:"usage_growth"`   // 使用量增长率
	BalanceGrowth float64 `json:"balance_growth"` // 余额增长率

	// 模块统计
	ModuleStats []ModuleStatsItem `json:"module_stats"`
}

// ModuleStatsItem 模块统计项
type ModuleStatsItem struct {
	Module  string  `json:"module"`  // 模块名称
	Name    string  `json:"name"`    // 显示名称
	Usage   int64   `json:"usage"`   // 使用量
	Revenue float64 `json:"revenue"` // 收入
	Keys    int64   `json:"keys"`    // Key数量
	Growth  float64 `json:"growth"`  // 增长率
	Color   string  `json:"color"`   // 颜色
}

// RechargeStats 充值统计数据结构
type RechargeStats struct {
	TodayAmount     float64 `json:"today_amount"`     // 今日充值金额
	MonthAmount     float64 `json:"month_amount"`     // 本月充值金额
	TotalCount      int64   `json:"total_count"`      // 总充值次数
	AverageAmount   float64 `json:"average_amount"`   // 平均充值金额
	PendingAmount   float64 `json:"pending_amount"`   // 待处理金额
	CompletedAmount float64 `json:"completed_amount"` // 已完成金额
	FailedAmount    float64 `json:"failed_amount"`    // 失败金额
	TotalUsers      int64   `json:"total_users"`      // 总用户数
	ActiveUsers     int64   `json:"active_users"`     // 活跃用户数
	TotalBalance    float64 `json:"total_balance"`    // 用户总余额
}

// KeyOverviewStats API Key概览统计
type KeyOverviewStats struct {
	TotalKeys       int64        `json:"total_keys"`        // 总Key数量
	ActiveKeys      int64        `json:"active_keys"`       // 活跃Key数量
	BlockedKeys     int64        `json:"blocked_keys"`      // 阻止Key数量
	KeysWithQuota   int64        `json:"keys_with_quota"`   // 有配额的Key数量
	KeysWithBalance int64        `json:"keys_with_balance"` // 用户有余额的Key数量
	TotalQuotaUsage float64      `json:"total_quota_usage"` // 总配额使用率
	AvgQuotaUsage   float64      `json:"avg_quota_usage"`   // 平均配额使用率
	ModuleStats     []ModuleStat `json:"module_stats"`      // 按模块统计
}

// ModuleStat 模块统计结构
type ModuleStat struct {
	Module      string  `json:"module"`       // 模块名称
	Name        string  `json:"name"`         // 模块显示名称
	KeyCount    int64   `json:"key_count"`    // Key数量
	ActiveCount int64   `json:"active_count"` // 活跃Key数量
	AvgUsage    float64 `json:"avg_usage"`    // 平均使用率
}

// UsageStats 使用量统计
type UsageStats struct {
	Period     string                 `json:"period"`      // 统计周期
	StartTime  int64                  `json:"start_time"`  // 开始时间
	EndTime    int64                  `json:"end_time"`    // 结束时间
	TotalCost  float64                `json:"total_cost"`  // 总成本
	TotalCount int64                  `json:"total_count"` // 总请求数
	ByModule   []ModuleUsageStatsItem `json:"by_module"`   // 按模块的详细统计
}

// ModuleUsageStatsItem 模块使用量统计项
type ModuleUsageStatsItem struct {
	Module     string  `json:"module"`      // 模块名称
	TotalUsage int64   `json:"total_usage"` // 总使用量
	TotalCost  float64 `json:"total_cost"`  // 总成本
	Count      int64   `json:"count"`       // 请求次数
	Unit       string  `json:"unit"`        // 单位
}

// GetDashboardStats 获取Dashboard统计数据
func (ss *StatService) GetDashboardStats(ctx context.Context) (*DashboardStats, error) {
	db := cosy.UseDB(ctx)

	// 获取当前时间
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	lastMonthStart := monthStart.AddDate(0, -1, 0)

	// 转换为毫秒时间戳
	todayStartMs := todayStart.UnixMilli()
	monthStartMs := monthStart.UnixMilli()
	lastMonthStartMs := lastMonthStart.UnixMilli()

	stats := &DashboardStats{}

	// 1. 用户统计
	if err := ss.getUserStats(db, stats); err != nil {
		logger.Error("获取用户统计失败", "error", err)
		return nil, err
	}

	// 2. API Key统计
	if err := ss.getKeyStats(db, stats); err != nil {
		logger.Error("获取Key统计失败", "error", err)
		return nil, err
	}

	// 3. 资源包统计
	if err := ss.getQuotaPackageStats(db, stats); err != nil {
		logger.Error("获取资源包统计失败", "error", err)
		return nil, err
	}

	// 4. 余额统计
	if err := ss.getBalanceStats(db, stats); err != nil {
		logger.Error("获取余额统计失败", "error", err)
		return nil, err
	}

	// 5. 收入统计
	if err := ss.getRevenueStats(db, stats, todayStartMs, monthStartMs); err != nil {
		logger.Error("获取收入统计失败", "error", err)
		return nil, err
	}

	// 6. 使用量统计
	if err := ss.getUsageStats(db, stats, todayStartMs, monthStartMs, now); err != nil {
		logger.Error("获取使用量统计失败", "error", err)
		return nil, err
	}

	// 7. 计算增长率
	if err := ss.calculateGrowthRates(db, stats, monthStartMs, lastMonthStartMs); err != nil {
		logger.Error("计算增长率失败", "error", err)
		return nil, err
	}

	// 8. 模块统计
	moduleStats, err := ss.getModuleStats(db, monthStartMs)
	if err != nil {
		logger.Error("获取模块统计失败", "error", err)
		return nil, err
	}
	stats.ModuleStats = moduleStats

	return stats, nil
}

// getUserStats 获取用户统计
func (ss *StatService) getUserStats(db *gorm.DB, stats *DashboardStats) error {
	// 总用户数
	if err := db.Model(&model.User{}).Count(&stats.TotalUsers).Error; err != nil {
		return fmt.Errorf("获取总用户数失败: %w", err)
	}

	// 活跃用户数（有余额的用户）
	if err := db.Model(&model.User{}).Where("balance > 0").Count(&stats.ActiveUsers).Error; err != nil {
		return fmt.Errorf("获取活跃用户数失败: %w", err)
	}

	return nil
}

// getKeyStats 获取API Key统计
func (ss *StatService) getKeyStats(db *gorm.DB, stats *DashboardStats) error {
	// 总Key数
	if err := db.Model(&model.ApiKey{}).Count(&stats.TotalKeys).Error; err != nil {
		return fmt.Errorf("获取总Key数失败: %w", err)
	}

	// 活跃Key数
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "ok").Count(&stats.ActiveKeys).Error; err != nil {
		return fmt.Errorf("获取活跃Key数失败: %w", err)
	}

	// 阻止Key数
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "blocked").Count(&stats.BlockedKeys).Error; err != nil {
		return fmt.Errorf("获取阻止Key数失败: %w", err)
	}

	return nil
}

// getQuotaPackageStats 获取资源包统计
func (ss *StatService) getQuotaPackageStats(db *gorm.DB, stats *DashboardStats) error {
	// 总资源包数
	if err := db.Model(&model.QuotaPackageRecord{}).Count(&stats.TotalQuotaPackages).Error; err != nil {
		return fmt.Errorf("获取总资源包数失败: %w", err)
	}

	// 活跃资源包数
	if err := db.Model(&model.QuotaPackageRecord{}).Where("status = ?", "active").Count(&stats.ActiveQuotaPackages).Error; err != nil {
		return fmt.Errorf("获取活跃资源包数失败: %w", err)
	}

	return nil
}

// getBalanceStats 获取余额统计
func (ss *StatService) getBalanceStats(db *gorm.DB, stats *DashboardStats) error {
	// 用户总余额
	if err := db.Model(&model.User{}).Select("COALESCE(SUM(balance), 0)").Scan(&stats.TotalBalance).Error; err != nil {
		return fmt.Errorf("获取用户总余额失败: %w", err)
	}

	// 平均用户余额
	if stats.TotalUsers > 0 {
		stats.AvgUserBalance = stats.TotalBalance / float64(stats.TotalUsers)
	}

	return nil
}

// getRevenueStats 获取收入统计
func (ss *StatService) getRevenueStats(db *gorm.DB, stats *DashboardStats, todayStartMs, monthStartMs int64) error {
	// 今日收入
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", "completed", todayStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TodayRevenue).Error; err != nil {
		return fmt.Errorf("获取今日收入失败: %w", err)
	}

	// 本月收入
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", "completed", monthStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.MonthRevenue).Error; err != nil {
		return fmt.Errorf("获取本月收入失败: %w", err)
	}

	// 总收入
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", "completed").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TotalRevenue).Error; err != nil {
		return fmt.Errorf("获取总收入失败: %w", err)
	}

	return nil
}

// getUsageStats 获取使用量统计
func (ss *StatService) getUsageStats(db *gorm.DB, stats *DashboardStats, todayStartMs, monthStartMs int64, now time.Time) error {
	// 今日使用量
	if err := db.Model(&model.UsageLog{}).
		Where("created_at >= ?", todayStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.TodayUsage).Error; err != nil {
		return fmt.Errorf("获取今日使用量失败: %w", err)
	}

	// 本月使用量
	if err := db.Model(&model.UsageLog{}).
		Where("created_at >= ?", monthStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.MonthUsage).Error; err != nil {
		return fmt.Errorf("获取本月使用量失败: %w", err)
	}

	// 总使用量
	if err := db.Model(&model.UsageLog{}).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.TotalUsage).Error; err != nil {
		return fmt.Errorf("获取总使用量失败: %w", err)
	}

	// 计算平均日使用量（基于本月数据）
	daysInMonth := now.Day()
	if daysInMonth > 0 {
		stats.AvgDailyUsage = stats.MonthUsage / int64(daysInMonth)
	}

	return nil
}

// calculateGrowthRates 计算增长率
func (ss *StatService) calculateGrowthRates(db *gorm.DB, stats *DashboardStats, monthStartMs, lastMonthStartMs int64) error {
	// 上月收入
	var lastMonthRevenue float64
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ? AND created_at < ?", "completed", lastMonthStartMs, monthStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&lastMonthRevenue).Error; err != nil {
		return fmt.Errorf("获取上月收入失败: %w", err)
	}

	if lastMonthRevenue > 0 {
		stats.RevenueGrowth = ((stats.MonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
	}

	// 上月新增用户数
	var thisMonthUsers, lastMonthUsers int64
	if err := db.Model(&model.User{}).Where("created_at >= ?", monthStartMs).Count(&thisMonthUsers).Error; err != nil {
		return fmt.Errorf("获取本月新增用户数失败: %w", err)
	}
	if err := db.Model(&model.User{}).Where("created_at >= ? AND created_at < ?", lastMonthStartMs, monthStartMs).Count(&lastMonthUsers).Error; err != nil {
		return fmt.Errorf("获取上月新增用户数失败: %w", err)
	}

	if lastMonthUsers > 0 {
		stats.UserGrowth = ((float64(thisMonthUsers) - float64(lastMonthUsers)) / float64(lastMonthUsers)) * 100
	}

	// 上月使用量
	var lastMonthUsage int64
	if err := db.Model(&model.UsageLog{}).
		Where("created_at >= ? AND created_at < ?", lastMonthStartMs, monthStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&lastMonthUsage).Error; err != nil {
		return fmt.Errorf("获取上月使用量失败: %w", err)
	}

	if lastMonthUsage > 0 {
		stats.UsageGrowth = ((float64(stats.MonthUsage) - float64(lastMonthUsage)) / float64(lastMonthUsage)) * 100
	}

	// 余额增长率（简化计算，基于本月充值）
	if stats.TotalBalance > 0 && stats.MonthRevenue > 0 {
		stats.BalanceGrowth = (stats.MonthRevenue / stats.TotalBalance) * 100
	}

	return nil
}

// getModuleStats 获取模块统计数据
func (ss *StatService) getModuleStats(db *gorm.DB, monthStartMs int64) ([]ModuleStatsItem, error) {
	type ModuleResult struct {
		Module  string  `json:"module"`
		Usage   int64   `json:"usage"`
		Revenue float64 `json:"revenue"`
		Keys    int64   `json:"keys"`
	}

	var results []ModuleResult

	// 查询各模块的统计数据
	err := db.Raw(`
		SELECT
			ul.module,
			COALESCE(SUM(ul.usage), 0) as usage,
			COALESCE(SUM(ul.cost), 0) as revenue,
			COUNT(DISTINCT ul.api_key) as keys
		FROM usage_logs ul
		WHERE ul.created_at >= ?
		GROUP BY ul.module
	`, monthStartMs).Scan(&results).Error

	if err != nil {
		return nil, fmt.Errorf("查询模块统计数据失败: %w", err)
	}

	// 转换为响应格式
	moduleStats := make([]ModuleStatsItem, 0, len(results))
	moduleNames := map[string]string{
		"llm": "LLM服务",
		"tts": "TTS服务",
		"asr": "ASR服务",
	}
	moduleColors := map[string]string{
		"llm": "bg-blue-500",
		"tts": "bg-green-500",
		"asr": "bg-purple-500",
	}

	for _, result := range results {
		name, exists := moduleNames[result.Module]
		if !exists {
			name = result.Module
		}

		color, exists := moduleColors[result.Module]
		if !exists {
			color = "bg-gray-500"
		}

		// 简化的增长率计算（这里可以根据需要添加更复杂的逻辑）
		growth := 0.0
		if result.Usage > 0 {
			// 可以添加与上月对比的逻辑
			growth = 5.0 // 临时值
		}

		moduleStats = append(moduleStats, ModuleStatsItem{
			Module:  result.Module,
			Name:    name,
			Usage:   result.Usage,
			Revenue: result.Revenue,
			Keys:    result.Keys,
			Growth:  growth,
			Color:   color,
		})
	}

	return moduleStats, nil
}

// GetRechargeStats 获取充值统计数据
func (ss *StatService) GetRechargeStats(ctx context.Context) (*RechargeStats, error) {
	db := cosy.UseDB(ctx)

	// 获取当前时间
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 转换为毫秒时间戳
	todayStartMs := todayStart.UnixMilli()
	monthStartMs := monthStart.UnixMilli()

	stats := &RechargeStats{}

	// 今日充值金额
	if err := db.Model(&model.RechargeRecord{}).
		Where("created_at >= ? AND status = ?", todayStartMs, types.RechargeStatusCompleted).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TodayAmount).Error; err != nil {
		return nil, fmt.Errorf("获取今日充值金额失败: %w", err)
	}

	// 本月充值金额
	if err := db.Model(&model.RechargeRecord{}).
		Where("created_at >= ? AND status = ?", monthStartMs, types.RechargeStatusCompleted).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.MonthAmount).Error; err != nil {
		return nil, fmt.Errorf("获取本月充值金额失败: %w", err)
	}

	// 总充值次数
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusCompleted).
		Count(&stats.TotalCount).Error; err != nil {
		return nil, fmt.Errorf("获取总充值次数失败: %w", err)
	}

	// 平均充值金额
	if stats.TotalCount > 0 {
		var totalAmount float64
		if err := db.Model(&model.RechargeRecord{}).
			Where("status = ?", types.RechargeStatusCompleted).
			Select("COALESCE(SUM(amount), 0)").
			Scan(&totalAmount).Error; err != nil {
			return nil, fmt.Errorf("获取总充值金额失败: %w", err)
		}
		stats.AverageAmount = totalAmount / float64(stats.TotalCount)
	}

	// 各状态金额统计
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusPending).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.PendingAmount).Error; err != nil {
		return nil, fmt.Errorf("获取待处理金额失败: %w", err)
	}

	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusCompleted).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.CompletedAmount).Error; err != nil {
		return nil, fmt.Errorf("获取已完成金额失败: %w", err)
	}

	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusFailed).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.FailedAmount).Error; err != nil {
		return nil, fmt.Errorf("获取失败金额失败: %w", err)
	}

	// 总用户数
	if err := db.Model(&model.User{}).Count(&stats.TotalUsers).Error; err != nil {
		return nil, fmt.Errorf("获取总用户数失败: %w", err)
	}

	// 活跃用户数（有余额的用户）
	if err := db.Model(&model.User{}).
		Where("balance > 0").
		Count(&stats.ActiveUsers).Error; err != nil {
		return nil, fmt.Errorf("获取活跃用户数失败: %w", err)
	}

	// 用户总余额
	if err := db.Model(&model.User{}).
		Select("COALESCE(SUM(balance), 0)").
		Scan(&stats.TotalBalance).Error; err != nil {
		return nil, fmt.Errorf("获取用户总余额失败: %w", err)
	}

	return stats, nil
}

// GetKeyOverviewStats 获取API Key概览统计
func (ss *StatService) GetKeyOverviewStats(ctx context.Context) (*KeyOverviewStats, error) {
	db := cosy.UseDB(ctx)

	stats := &KeyOverviewStats{}

	// 1. 获取基础统计数据
	// 总Key数量
	if err := db.Model(&model.ApiKey{}).Count(&stats.TotalKeys).Error; err != nil {
		return nil, fmt.Errorf("获取总Key数失败: %w", err)
	}

	// 活跃Key数量
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "ok").Count(&stats.ActiveKeys).Error; err != nil {
		return nil, fmt.Errorf("获取活跃Key数失败: %w", err)
	}

	// 阻止Key数量
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "blocked").Count(&stats.BlockedKeys).Error; err != nil {
		return nil, fmt.Errorf("获取阻止Key数失败: %w", err)
	}

	// 2. 有配额的Key数量
	if err := db.Raw(`
		SELECT COUNT(DISTINCT api_key)
		FROM quota_package_records
		WHERE api_key IS NOT NULL AND api_key != '' AND status = 'active'
	`).Scan(&stats.KeysWithQuota).Error; err != nil {
		return nil, fmt.Errorf("获取有配额Key数失败: %w", err)
	}

	// 3. 用户有余额的Key数量
	if err := db.Raw(`
		SELECT COUNT(DISTINCT ak.api_key)
		FROM api_keys ak
		JOIN users u ON ak.user_id = u.id
		WHERE u.balance > 0
	`).Scan(&stats.KeysWithBalance).Error; err != nil {
		return nil, fmt.Errorf("获取用户有余额Key数失败: %w", err)
	}

	// 4. 计算配额使用率统计
	type QuotaUsageResult struct {
		TotalQuota int64   `json:"total_quota"`
		TotalUsed  int64   `json:"total_used"`
		AvgUsage   float64 `json:"avg_usage"`
	}

	var quotaUsage QuotaUsageResult
	if err := db.Model(&model.QuotaPackageRecord{}).
		Select("SUM(quota) as total_quota, SUM(used) as total_used, AVG(CASE WHEN quota > 0 THEN (used * 100.0 / quota) ELSE 0 END) as avg_usage").
		Where("status = 'active'").
		Scan(&quotaUsage).Error; err != nil {
		return nil, fmt.Errorf("计算配额使用率失败: %w", err)
	}

	if quotaUsage.TotalQuota > 0 {
		stats.TotalQuotaUsage = float64(quotaUsage.TotalUsed) * 100.0 / float64(quotaUsage.TotalQuota)
	}
	stats.AvgQuotaUsage = quotaUsage.AvgUsage

	// 5. 按模块统计
	moduleStats, err := ss.getKeyModuleStats(db)
	if err != nil {
		return nil, fmt.Errorf("获取模块统计失败: %w", err)
	}
	stats.ModuleStats = moduleStats

	return stats, nil
}

// getKeyModuleStats 获取Key模块统计
func (ss *StatService) getKeyModuleStats(db *gorm.DB) ([]ModuleStat, error) {
	type ModuleStatsResult struct {
		Module      string  `json:"module"`
		KeyCount    int64   `json:"key_count"`
		ActiveCount int64   `json:"active_count"`
		AvgUsage    float64 `json:"avg_usage"`
	}

	var moduleStatsResults []ModuleStatsResult

	// 由于ApiKey模型中没有module字段，我们需要通过配额包记录来统计
	if err := db.Raw(`
		SELECT
			qpr.module,
			COUNT(DISTINCT qpr.api_key) as key_count,
			COUNT(DISTINCT CASE WHEN ak.status = 'ok' THEN qpr.api_key END) as active_count,
			AVG(CASE WHEN qpr.quota > 0 THEN (qpr.used * 100.0 / qpr.quota) ELSE 0 END) as avg_usage
		FROM quota_package_records qpr
		LEFT JOIN api_keys ak ON qpr.api_key = ak.api_key
		WHERE qpr.api_key IS NOT NULL AND qpr.api_key != '' AND qpr.status = 'active'
		GROUP BY qpr.module
	`).Scan(&moduleStatsResults).Error; err != nil {
		return nil, fmt.Errorf("查询模块统计失败: %w", err)
	}

	// 转换为响应格式
	moduleStats := make([]ModuleStat, 0, len(moduleStatsResults))
	moduleNameMap := map[string]string{
		"llm": "LLM服务",
		"tts": "TTS服务",
		"asr": "ASR服务",
	}

	for _, result := range moduleStatsResults {
		name := moduleNameMap[result.Module]
		if name == "" {
			name = result.Module
		}

		moduleStats = append(moduleStats, ModuleStat{
			Module:      result.Module,
			Name:        name,
			KeyCount:    result.KeyCount,
			ActiveCount: result.ActiveCount,
			AvgUsage:    result.AvgUsage,
		})
	}

	return moduleStats, nil
}

// GetUsageStats 获取使用量统计
func (ss *StatService) GetUsageStats(ctx context.Context, apiKey, period string) (*UsageStats, error) {
	db := cosy.UseDB(ctx)

	var startTime time.Time
	now := time.Now()

	switch period {
	case "day":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case "week":
		startTime = now.AddDate(0, 0, -7)
	case "month":
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	default:
		startTime = time.Date(2025, 1, 1, 0, 0, 0, 0, now.Location())
	}

	// 查询统计数据，按模块分开统计（不同模块的usage单位不同，不能直接相加）
	var moduleStats []ModuleUsageStatsItem
	query := db.Model(model.UsageLog{}).
		Select("module, SUM(`usage`) as total_usage, SUM(`cost`) as total_cost, COUNT(*) as count, MAX(`unit`) as unit").
		Where("created_at >= ?", startTime.UnixMilli()).
		Group("module")

	if apiKey != "" {
		query = query.Where("api_key = ?", apiKey)
	}

	if err := query.Find(&moduleStats).Error; err != nil {
		return nil, fmt.Errorf("查询使用量统计失败: %w", err)
	}

	// 只计算总成本和总请求数，不计算总usage（因为单位不同）
	var totalCost float64
	var totalCount int64

	for _, stat := range moduleStats {
		totalCost += stat.TotalCost
		totalCount += stat.Count
	}

	stats := &UsageStats{
		Period:     period,
		StartTime:  startTime.UnixMilli(),
		EndTime:    now.UnixMilli(),
		TotalCost:  totalCost,
		TotalCount: totalCount,
		ByModule:   moduleStats,
	}

	return stats, nil
}

# 统计服务 (StatService) 使用说明

## 概述

StatService 是一个统一的统计服务，封装了所有与统计相关的业务逻辑，包括：
- Dashboard 统计数据
- 充值统计数据
- API Key 概览统计
- 使用量统计数据

## 架构设计

### 服务层结构
```
internal/billing/
├── stat_service.go          # 统计服务主文件
├── stat_service_test.go     # 统计服务测试
├── billing_service.go       # 计费服务主文件（已集成StatService）
└── ...
```

### API层结构
```
api/
├── admin/dashboard_handler.go    # Dashboard接口（已更新使用StatService）
└── billing/
    ├── recharge_handler.go       # 充值接口（已更新使用StatService）
    ├── api_key_handler.go        # API Key接口（已更新使用StatService）
    └── usage_handler.go          # 使用量接口（已更新使用StatService）
```

## 主要功能

### 1. Dashboard 统计 (GetDashboardStats)

获取完整的Dashboard统计数据，包括：
- **核心指标**: 总收入、本月收入、今日收入、用户数、Key数等
- **计费相关**: 资源包统计、用户余额统计
- **使用量统计**: 总使用量、本月使用量、今日使用量、平均日使用量
- **趋势数据**: 收入增长率、用户增长率、使用量增长率、余额增长率
- **模块统计**: 按模块（LLM、TTS、ASR）的详细统计

**API接口**: `GET /api/admin/dashboard/stats`

### 2. 充值统计 (GetRechargeStats)

获取充值相关的统计数据，包括：
- 今日充值金额、本月充值金额
- 总充值次数、平均充值金额
- 各状态金额统计（待处理、已完成、失败）
- 用户统计（总用户数、活跃用户数、总余额）

**API接口**: `GET /api/billing/recharge/stats`

### 3. API Key 概览统计 (GetKeyOverviewStats)

获取API Key的概览统计数据，包括：
- Key数量统计（总数、活跃数、阻止数）
- 配额统计（有配额的Key数、配额使用率）
- 用户余额相关的Key统计
- 按模块的Key统计

**API接口**: `GET /api/billing/keys/overview/stats`

### 4. 使用量统计 (GetUsageStats)

获取指定API Key的使用量统计数据，包括：
- 按时间周期统计（日、周、月、全部）
- 按模块分别统计（因为不同模块单位不同）
- 总成本、总请求数统计

**API接口**: `GET /api/billing/keys/{key}/stats?period=month`

## 使用方法

### 1. 在Handler中使用

```go
func YourHandler(c *gin.Context) {
    // 获取统计服务实例
    statService := billing.GetStatServiceInstance()
    if statService == nil {
        logger.Error("获取统计服务失败")
        c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
        return
    }

    // 调用统计服务获取数据
    stats, err := statService.GetDashboardStats(c.Request.Context())
    if err != nil {
        logger.Error("获取统计数据失败", "error", err)
        c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
        return
    }

    c.JSON(http.StatusOK, stats)
}
```

### 2. 在Service中使用

```go
func YourService() {
    // 获取统计服务实例
    statService := billing.GetStatServiceInstance()
    if statService == nil {
        return
    }

    // 调用统计方法
    ctx := context.Background()
    stats, err := statService.GetRechargeStats(ctx)
    if err != nil {
        logger.Error("获取充值统计失败", "error", err)
        return
    }

    // 处理统计数据
    // ...
}
```

## 数据结构

### DashboardStats
```go
type DashboardStats struct {
    // 核心指标
    TotalRevenue float64 `json:"total_revenue"`
    MonthRevenue float64 `json:"month_revenue"`
    TodayRevenue float64 `json:"today_revenue"`
    TotalUsers   int64   `json:"total_users"`
    ActiveUsers  int64   `json:"active_users"`
    TotalKeys    int64   `json:"total_keys"`
    ActiveKeys   int64   `json:"active_keys"`
    BlockedKeys  int64   `json:"blocked_keys"`
    
    // 计费相关
    TotalQuotaPackages  int64   `json:"total_quota_packages"`
    ActiveQuotaPackages int64   `json:"active_quota_packages"`
    TotalBalance        float64 `json:"total_balance"`
    AvgUserBalance      float64 `json:"avg_user_balance"`
    
    // 使用量统计
    TotalUsage    int64 `json:"total_usage"`
    MonthUsage    int64 `json:"month_usage"`
    TodayUsage    int64 `json:"today_usage"`
    AvgDailyUsage int64 `json:"avg_daily_usage"`
    
    // 趋势数据
    RevenueGrowth float64 `json:"revenue_growth"`
    UserGrowth    float64 `json:"user_growth"`
    UsageGrowth   float64 `json:"usage_growth"`
    BalanceGrowth float64 `json:"balance_growth"`
    
    // 模块统计
    ModuleStats []ModuleStatsItem `json:"module_stats"`
}
```

## 优势

1. **统一管理**: 所有统计相关的逻辑都集中在StatService中
2. **代码复用**: 避免在多个Handler中重复统计逻辑
3. **易于维护**: 统计逻辑的修改只需要在一个地方进行
4. **性能优化**: 可以在Service层进行缓存和优化
5. **测试友好**: 统计逻辑可以独立测试
6. **错误处理**: 统一的错误处理和日志记录

## 扩展

如需添加新的统计功能：

1. 在 `StatService` 中添加新的方法
2. 在相应的Handler中调用新方法
3. 添加相应的测试用例
4. 更新API文档

## 测试

运行统计服务测试：
```bash
go test ./internal/billing -v -run TestStatService
```

package dashboard

import (
	"fmt"
	"net/http"
	"time"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// DashboardStatsResponse Dashboard统计响应结构
type DashboardStatsResponse struct {
	// 核心指标
	TotalRevenue float64 `json:"total_revenue"` // 总收入
	MonthRevenue float64 `json:"month_revenue"` // 本月收入
	TodayRevenue float64 `json:"today_revenue"` // 今日收入
	TotalUsers   int64   `json:"total_users"`   // 总用户数
	ActiveUsers  int64   `json:"active_users"`  // 活跃用户数
	TotalKeys    int64   `json:"total_keys"`    // 总Key数
	ActiveKeys   int64   `json:"active_keys"`   // 活跃Key数
	BlockedKeys  int64   `json:"blocked_keys"`  // 阻止Key数

	// 计费相关
	TotalQuotaPackages  int64   `json:"total_quota_packages"`  // 总资源包数
	ActiveQuotaPackages int64   `json:"active_quota_packages"` // 活跃资源包数
	TotalBalance        float64 `json:"total_balance"`         // 用户总余额
	AvgUserBalance      float64 `json:"avg_user_balance"`      // 平均用户余额

	// 使用量统计
	TotalUsage    int64 `json:"total_usage"`     // 总使用量
	MonthUsage    int64 `json:"month_usage"`     // 本月使用量
	TodayUsage    int64 `json:"today_usage"`     // 今日使用量
	AvgDailyUsage int64 `json:"avg_daily_usage"` // 平均日使用量

	// 趋势数据
	RevenueGrowth float64 `json:"revenue_growth"` // 收入增长率
	UserGrowth    float64 `json:"user_growth"`    // 用户增长率
	UsageGrowth   float64 `json:"usage_growth"`   // 使用量增长率
	BalanceGrowth float64 `json:"balance_growth"` // 余额增长率

	// 模块统计
	ModuleStats []ModuleStatsItem `json:"module_stats"`
}

// ModuleStatsItem 模块统计项
type ModuleStatsItem struct {
	Module  string  `json:"module"`  // 模块名称
	Name    string  `json:"name"`    // 显示名称
	Usage   int64   `json:"usage"`   // 使用量
	Revenue float64 `json:"revenue"` // 收入
	Keys    int64   `json:"keys"`    // Key数量
	Growth  float64 `json:"growth"`  // 增长率
	Color   string  `json:"color"`   // 颜色
}

// RecentActiveUser 近期活跃用户
type RecentActiveUser struct {
	ID         string  `json:"id"`
	Name       string  `json:"name"`
	Email      string  `json:"email"`
	Balance    float64 `json:"balance"`
	Usage      int64   `json:"usage"`
	LastActive string  `json:"last_active"`
}

// SystemHealthStatus 系统健康状态
type SystemHealthStatus struct {
	APIResponseTime   int     `json:"api_response_time"`  // API响应时间(ms)
	ErrorRate         float64 `json:"error_rate"`         // 错误率
	Uptime            float64 `json:"uptime"`             // 系统可用性
	QueueDepth        int     `json:"queue_depth"`        // 队列深度
	ActiveConnections int64   `json:"active_connections"` // 活跃连接数
	LastCheck         string  `json:"last_check"`         // 最后检查时间
}

// RecentRecharge 近期充值记录
type RecentRecharge struct {
	ID     string  `json:"id"`
	User   string  `json:"user"`
	Amount float64 `json:"amount"`
	Type   string  `json:"type"`
	Time   string  `json:"time"`
}

// DashboardDataResponse Dashboard完整数据响应
type DashboardDataResponse struct {
	Stats             DashboardStatsResponse `json:"stats"`
	RecentActiveUsers []RecentActiveUser     `json:"recent_active_users"`
	SystemHealth      SystemHealthStatus     `json:"system_health"`
	RecentRecharges   []RecentRecharge       `json:"recent_recharges"`
}

// GetDashboardStats 获取Dashboard统计数据
func GetDashboardStats(c *gin.Context) {
	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取数据
	stats, err := statService.GetDashboardStats(c.Request.Context())
	if err != nil {
		logger.Error("获取Dashboard统计数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// calculateGrowthRates 计算增长率
func calculateGrowthRates(db *gorm.DB, stats *DashboardStatsResponse, monthStartMs, lastMonthStartMs int64) {
	// 上月收入
	var lastMonthRevenue float64
	db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ? AND created_at < ?", "completed", lastMonthStartMs, monthStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&lastMonthRevenue)

	if lastMonthRevenue > 0 {
		stats.RevenueGrowth = ((stats.MonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
	}

	// 上月新增用户数
	var thisMonthUsers, lastMonthUsers int64
	db.Model(&model.User{}).Where("created_at >= ?", monthStartMs).Count(&thisMonthUsers)
	db.Model(&model.User{}).Where("created_at >= ? AND created_at < ?", lastMonthStartMs, monthStartMs).Count(&lastMonthUsers)

	if lastMonthUsers > 0 {
		stats.UserGrowth = ((float64(thisMonthUsers) - float64(lastMonthUsers)) / float64(lastMonthUsers)) * 100
	}

	// 上月使用量
	var lastMonthUsage int64
	db.Model(&model.UsageLog{}).
		Where("created_at >= ? AND created_at < ?", lastMonthStartMs, monthStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&lastMonthUsage)

	if lastMonthUsage > 0 {
		stats.UsageGrowth = ((float64(stats.MonthUsage) - float64(lastMonthUsage)) / float64(lastMonthUsage)) * 100
	}

	// 余额增长率（简化计算，基于本月充值）
	if stats.TotalBalance > 0 && stats.MonthRevenue > 0 {
		stats.BalanceGrowth = (stats.MonthRevenue / stats.TotalBalance) * 100
	}
}

// getModuleStats 获取模块统计数据
func getModuleStats(db *gorm.DB, monthStartMs int64) []ModuleStatsItem {
	type ModuleResult struct {
		Module  string  `json:"module"`
		Usage   int64   `json:"usage"`
		Revenue float64 `json:"revenue"`
		Keys    int64   `json:"keys"`
	}

	var results []ModuleResult

	// 查询各模块的统计数据
	db.Raw(`
		SELECT
			ul.module,
			COALESCE(SUM(ul.usage), 0) as usage,
			COALESCE(SUM(ul.cost), 0) as revenue,
			COUNT(DISTINCT ul.api_key) as keys
		FROM usage_logs ul
		WHERE ul.created_at >= ?
		GROUP BY ul.module
	`, monthStartMs).Scan(&results)

	// 转换为响应格式
	moduleStats := make([]ModuleStatsItem, 0, len(results))
	moduleNames := map[string]string{
		"llm": "LLM服务",
		"tts": "TTS服务",
		"asr": "ASR服务",
	}
	moduleColors := map[string]string{
		"llm": "bg-blue-500",
		"tts": "bg-green-500",
		"asr": "bg-purple-500",
	}

	for _, result := range results {
		name, exists := moduleNames[result.Module]
		if !exists {
			name = result.Module
		}

		color, exists := moduleColors[result.Module]
		if !exists {
			color = "bg-gray-500"
		}

		// 简化的增长率计算（这里可以根据需要添加更复杂的逻辑）
		growth := 0.0
		if result.Usage > 0 {
			// 可以添加与上月对比的逻辑
			growth = 5.0 // 临时值
		}

		moduleStats = append(moduleStats, ModuleStatsItem{
			Module:  result.Module,
			Name:    name,
			Usage:   result.Usage,
			Revenue: result.Revenue,
			Keys:    result.Keys,
			Growth:  growth,
			Color:   color,
		})
	}

	return moduleStats
}

// GetDashboardData 获取Dashboard完整数据
func GetDashboardData(c *gin.Context) {
	db := cosy.UseDB(c)

	var response DashboardDataResponse

	// 获取统计数据
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	lastMonthStart := monthStart.AddDate(0, -1, 0)

	todayStartMs := todayStart.UnixMilli()
	monthStartMs := monthStart.UnixMilli()
	lastMonthStartMs := lastMonthStart.UnixMilli()

	// 1. 获取基础统计数据
	var stats DashboardStatsResponse

	// 用户统计
	db.Model(&model.User{}).Count(&stats.TotalUsers)
	db.Model(&model.User{}).Where("balance > 0").Count(&stats.ActiveUsers)

	// API Key统计
	db.Model(&model.ApiKey{}).Count(&stats.TotalKeys)
	db.Model(&model.ApiKey{}).Where("status = ?", "ok").Count(&stats.ActiveKeys)
	db.Model(&model.ApiKey{}).Where("status = ?", "blocked").Count(&stats.BlockedKeys)

	// 资源包统计
	db.Model(&model.QuotaPackageRecord{}).Count(&stats.TotalQuotaPackages)
	db.Model(&model.QuotaPackageRecord{}).Where("status = ?", "active").Count(&stats.ActiveQuotaPackages)

	// 余额统计
	db.Model(&model.User{}).Select("COALESCE(SUM(balance), 0)").Scan(&stats.TotalBalance)
	if stats.TotalUsers > 0 {
		stats.AvgUserBalance = stats.TotalBalance / float64(stats.TotalUsers)
	}

	// 收入统计
	db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", "completed", todayStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TodayRevenue)

	db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", "completed", monthStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.MonthRevenue)

	db.Model(&model.RechargeRecord{}).
		Where("status = ?", "completed").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TotalRevenue)

	// 使用量统计
	db.Model(&model.UsageLog{}).
		Where("created_at >= ?", todayStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.TodayUsage)

	db.Model(&model.UsageLog{}).
		Where("created_at >= ?", monthStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.MonthUsage)

	db.Model(&model.UsageLog{}).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.TotalUsage)

	daysInMonth := now.Day()
	if daysInMonth > 0 {
		stats.AvgDailyUsage = stats.MonthUsage / int64(daysInMonth)
	}

	// 计算增长率
	calculateGrowthRates(db, &stats, monthStartMs, lastMonthStartMs)

	// 模块统计
	stats.ModuleStats = getModuleStats(db, monthStartMs)

	response.Stats = stats

	// 2. 获取近期活跃用户
	response.RecentActiveUsers = getRecentActiveUsers(db)

	// 3. 获取系统健康状态
	response.SystemHealth = getSystemHealth()

	// 4. 获取近期充值记录
	response.RecentRecharges = getRecentRecharges(db)

	c.JSON(http.StatusOK, response)
}

// getRecentActiveUsers 获取近期活跃用户
func getRecentActiveUsers(db *gorm.DB) []RecentActiveUser {
	var users []model.User

	// 获取最近活跃的用户（有余额且最近有活动）
	db.Where("balance > 0").
		Order("last_active DESC").
		Limit(5).
		Find(&users)

	recentUsers := make([]RecentActiveUser, 0, len(users))
	for _, user := range users {
		// 计算用户的总使用量
		var totalUsage int64
		db.Raw(`
			SELECT COALESCE(SUM(ul.usage), 0)
			FROM usage_logs ul
			JOIN api_keys ak ON ul.api_key = ak.api_key
			WHERE ak.user_id = ?
		`, user.ID).Scan(&totalUsage)

		// 格式化最后活跃时间
		lastActive := "未知"
		if user.LastActive > 0 {
			lastActiveTime := time.Unix(user.LastActive, 0)
			now := time.Now()
			diff := now.Sub(lastActiveTime)

			if diff < time.Minute {
				lastActive = "刚刚"
			} else if diff < time.Hour {
				lastActive = fmt.Sprintf("%d分钟前", int(diff.Minutes()))
			} else if diff < 24*time.Hour {
				lastActive = fmt.Sprintf("%d小时前", int(diff.Hours()))
			} else {
				lastActive = fmt.Sprintf("%d天前", int(diff.Hours()/24))
			}
		}

		recentUsers = append(recentUsers, RecentActiveUser{
			ID:         fmt.Sprintf("%d", user.ID),
			Name:       user.Name,
			Email:      user.Email,
			Balance:    user.Balance,
			Usage:      totalUsage,
			LastActive: lastActive,
		})
	}

	return recentUsers
}

// getSystemHealth 获取系统健康状态
func getSystemHealth() SystemHealthStatus {
	// 这里可以添加真实的系统监控逻辑
	// 目前返回模拟数据
	return SystemHealthStatus{
		APIResponseTime:   125,
		ErrorRate:         0.02,
		Uptime:            99.9,
		QueueDepth:        3,
		ActiveConnections: 1247,
		LastCheck:         time.Now().Format(time.RFC3339),
	}
}

// getRecentRecharges 获取近期充值记录
func getRecentRecharges(db *gorm.DB) []RecentRecharge {
	var records []model.RechargeRecord

	// 获取最近的充值记录
	db.Preload("User").
		Where("status = ?", "completed").
		Order("created_at DESC").
		Limit(5).
		Find(&records)

	recentRecharges := make([]RecentRecharge, 0, len(records))
	for _, record := range records {
		userName := "未知用户"
		if record.User != nil {
			userName = record.User.Name
		}

		// 格式化时间
		createdTime := time.UnixMilli(record.CreatedAt)
		now := time.Now()
		diff := now.Sub(createdTime)

		timeStr := "未知"
		if diff < time.Minute {
			timeStr = "刚刚"
		} else if diff < time.Hour {
			timeStr = fmt.Sprintf("%d分钟前", int(diff.Minutes()))
		} else if diff < 24*time.Hour {
			timeStr = fmt.Sprintf("%d小时前", int(diff.Hours()))
		} else {
			timeStr = fmt.Sprintf("%d天前", int(diff.Hours()/24))
		}

		recentRecharges = append(recentRecharges, RecentRecharge{
			ID:     fmt.Sprintf("%d", record.ID),
			User:   userName,
			Amount: record.Amount,
			Type:   record.Type,
			Time:   timeStr,
		})
	}

	return recentRecharges
}

// InitDashboardRouter 初始化Dashboard路由
func InitDashboardRouter(g *gin.RouterGroup) {
	g.GET("/dashboard/stats", GetDashboardStats)
	g.GET("/dashboard/data", GetDashboardData)
}

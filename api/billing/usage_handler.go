package billing

import (
	"errors"
	"net/http"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// UsageHistoryResponse 用量历史响应结构
type UsageHistoryResponse struct {
	Total int64            `json:"total"`
	Items []model.UsageLog `json:"items"`
}

// GetKeyUsageHistory 获取用量历史
func GetKeyUsageHistory(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	cosy.Core[model.UsageLog](c).SetValidRules(gin.H{
		"module":     "in",
		"start_time": "gte",
		"end_time":   "lte",
	}).GormScope(func(tx *gorm.DB) *gorm.DB {
		return tx.Where("api_key = ?", key)
	}).PagingList()
}

// GetKeyUsageStats 获取用量统计
func GetKeyUsageStats(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	period := c.DefaultQuery("period", "month")

	var startTime time.Time
	now := time.Now()

	switch period {
	case "day":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case "week":
		startTime = now.AddDate(0, 0, -7)
	case "month":
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	default:
		startTime = time.Date(2025, 1, 1, 0, 0, 0, 0, now.Location())
	}

	// 查询统计数据，按模块分开统计（不同模块的usage单位不同，不能直接相加）
	type ModuleStatsResult struct {
		Module     string  `json:"module"`
		TotalUsage int64   `json:"total_usage"` // 改名为usage，因为不同模块单位不同
		TotalCost  float64 `json:"total_cost"`
		Count      int64   `json:"count"`
		Unit       string  `json:"unit"` // 添加单位字段
	}

	var moduleStats []ModuleStatsResult
	err := cosy.UseDB(c).Model(model.UsageLog{}).
		Select("module, SUM(`usage`) as total_usage, SUM(`cost`) as total_cost, COUNT(*) as count, MAX(`unit`) as unit").
		Where("api_key = ? AND created_at >= ?", key, startTime.UnixMilli()).
		Group("module").
		Find(&moduleStats).
		Error

	if err != nil {
		logger.Error("Failed to query usage statistics", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 只计算总成本和总请求数，不计算总usage（因为单位不同）
	var totalCost float64
	var totalCount int64

	for _, stat := range moduleStats {
		totalCost += stat.TotalCost
		totalCount += stat.Count
	}

	response := gin.H{
		"period":      period,
		"start_time":  startTime.UnixMilli(),
		"end_time":    now.UnixMilli(),
		"total_cost":  totalCost,   // 总成本
		"total_count": totalCount,  // 总请求数
		"by_module":   moduleStats, // 按模块的详细统计
	}

	c.JSON(http.StatusOK, response)
}

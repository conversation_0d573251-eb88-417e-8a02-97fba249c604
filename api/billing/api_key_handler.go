package billing

import (
	"errors"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// KeyStatusResponse Key状态响应结构
type KeyStatusResponse struct {
	ApiKey    string                      `json:"api_key"`
	Status    string                      `json:"status"`
	Available bool                        `json:"available"`
	Quotas    []*model.QuotaPackageRecord `json:"quotas"`
	User      *model.User                 `json:"user,omitempty"`
	CreatedAt int64                       `json:"created_at"`
	UpdatedAt int64                       `json:"updated_at"`
}

// KeyOverviewStats API Key概览统计响应结构
type KeyOverviewStats struct {
	TotalKeys       int64        `json:"total_keys"`        // 总Key数量
	ActiveKeys      int64        `json:"active_keys"`       // 活跃Key数量
	BlockedKeys     int64        `json:"blocked_keys"`      // 阻止Key数量
	KeysWithQuota   int64        `json:"keys_with_quota"`   // 有配额的Key数量
	KeysWithBalance int64        `json:"keys_with_balance"` // 用户有余额的Key数量
	TotalQuotaUsage float64      `json:"total_quota_usage"` // 总配额使用率
	AvgQuotaUsage   float64      `json:"avg_quota_usage"`   // 平均配额使用率
	ModuleStats     []ModuleStat `json:"module_stats"`      // 按模块统计
}

// ModuleStat 模块统计结构
type ModuleStat struct {
	Module      string  `json:"module"`       // 模块名称
	Name        string  `json:"name"`         // 模块显示名称
	KeyCount    int64   `json:"key_count"`    // Key数量
	ActiveCount int64   `json:"active_count"` // 活跃Key数量
	AvgUsage    float64 `json:"avg_usage"`    // 平均使用率
}

// InitKeyCurdAPI 初始化API密钥管理接口
func InitKeyCurdAPI(g *gin.RouterGroup) {
	g.GET("/keys", GetKeyList)
	g.GET("/keys/:key", GetKey)
	g.PUT("/keys/:key", UpdateKey)
	g.DELETE("/keys/:key", DeleteKey)
	g.POST("/keys", CreateKey)
	g.GET("/keys/overview/stats", GetKeyOverviewStats)
}

// GetKeyList 获取Key列表
func GetKeyList(c *gin.Context) {
	core := cosy.Core[model.ApiKey](c)

	core.SetPreloads("User")

	core.SetEqual("user_id")

	core.PagingList()
}

// CreateKey 创建Key
func CreateKey(c *gin.Context) {
	core := cosy.Core[model.ApiKey](c).SetValidRules(gin.H{
		"name":    "required",
		"user_id": "required",
		"module":  "required",
		"status":  "omitempty",
		"comment": "omitempty",
	})

	core.BeforeExecuteHook(func(ctx *cosy.Ctx[model.ApiKey]) {
		ctx.Model.APIKey = uuid.New().String()
	})

	core.Create()
}

// UpdateKeyRequest 更新Key请求结构
type UpdateKeyRequest struct {
	Name    string `json:"name"`
	Comment string `json:"comment"`
	Status  string `json:"status"`
}

// GetKey 获取Key
func GetKey(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	apiKey, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.APIKey.Eq(key)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// UpdateKey 更新Key
func UpdateKey(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	body := &UpdateKeyRequest{}
	if err := c.ShouldBindJSON(body); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	query.ApiKey.
		Where(query.ApiKey.APIKey.Eq(key)).
		Updates(body)

	GetKey(c)
}

// DeleteKey 删除Key
func DeleteKey(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	query.ApiKey.
		Where(query.ApiKey.APIKey.Eq(key)).
		Delete()

	c.JSON(http.StatusOK, gin.H{"message": "Key deleted successfully"})
}

// GetKeyStatus 获取Key状态
func GetKeyStatus(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	// 查询Key记录
	apiKey, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.APIKey.Eq(key)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 查询该类型的专属配额信息
	var quotas []*model.QuotaPackageRecord
	err = query.QuotaPackageRecord.UnderlyingDB().
		Where("(api_key IN ? OR api_key IS NULL)", []string{key, ""}).
		Where("user_id = ?", apiKey.UserID).
		Find(&quotas).Error
	if err != nil {
		logger.Error("Failed to query quotas", "error", err, "key_id", apiKey.ID)
		cosy.ErrHandler(c, err)
		return
	}

	// 计算是否可用（只检查对应类型的配额）
	available := apiKey.Status == "ok"
	if len(quotas) > 0 {
		for _, quota := range quotas {
			if quota.Quota > 0 && quota.Used >= quota.Quota && apiKey.User.Balance <= 0 {
				available = false
				break
			}
		}
	} else {
		// 没有该类型的配额，检查用户余额
		if apiKey.User == nil || apiKey.User.Balance <= 0 {
			available = false
		}
	}

	response := KeyStatusResponse{
		ApiKey:    apiKey.APIKey,
		Status:    apiKey.Status,
		Available: available,
		Quotas:    quotas,
		User:      apiKey.User,
		CreatedAt: apiKey.CreatedAt,
		UpdatedAt: apiKey.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// GetKeyOverviewStats 获取API Key概览统计
func GetKeyOverviewStats(c *gin.Context) {
	db := cosy.UseDB(c)

	// 1. 获取基础统计数据
	var totalKeys, activeKeys, blockedKeys int64

	// 总Key数量
	if err := db.Model(&model.ApiKey{}).Count(&totalKeys).Error; err != nil {
		logger.Error("Failed to count total keys", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 活跃Key数量
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "ok").Count(&activeKeys).Error; err != nil {
		logger.Error("Failed to count active keys", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 阻止Key数量
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "blocked").Count(&blockedKeys).Error; err != nil {
		logger.Error("Failed to count blocked keys", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 2. 获取有配额的Key数量（通过子查询）
	var keysWithQuota int64
	subQuery := db.Model(&model.QuotaPackageRecord{}).
		Select("DISTINCT api_key").
		Where("api_key IS NOT NULL AND api_key != '' AND status = 'active'")

	if err := db.Model(&model.ApiKey{}).
		Where("api_key IN (?)", subQuery).
		Count(&keysWithQuota).Error; err != nil {
		logger.Error("Failed to count keys with quota", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 3. 获取用户有余额的Key数量
	var keysWithBalance int64
	if err := db.Model(&model.ApiKey{}).
		Joins("JOIN users ON api_keys.user_id = users.id").
		Where("users.balance > 0").
		Count(&keysWithBalance).Error; err != nil {
		logger.Error("Failed to count keys with balance", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 4. 计算配额使用率统计
	type QuotaUsageResult struct {
		TotalQuota int64   `json:"total_quota"`
		TotalUsed  int64   `json:"total_used"`
		AvgUsage   float64 `json:"avg_usage"`
	}

	var quotaUsage QuotaUsageResult
	if err := db.Model(&model.QuotaPackageRecord{}).
		Select("SUM(quota) as total_quota, SUM(used) as total_used, AVG(CASE WHEN quota > 0 THEN (used * 100.0 / quota) ELSE 0 END) as avg_usage").
		Where("status = 'active'").
		Scan(&quotaUsage).Error; err != nil {
		logger.Error("Failed to calculate quota usage", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	totalQuotaUsage := 0.0
	if quotaUsage.TotalQuota > 0 {
		totalQuotaUsage = float64(quotaUsage.TotalUsed) * 100.0 / float64(quotaUsage.TotalQuota)
	}

	// 5. 按模块统计
	type ModuleStatsResult struct {
		Module      string  `json:"module"`
		KeyCount    int64   `json:"key_count"`
		ActiveCount int64   `json:"active_count"`
		AvgUsage    float64 `json:"avg_usage"`
	}

	var moduleStatsResults []ModuleStatsResult

	// 由于ApiKey模型中没有module字段，我们需要通过配额包记录来统计
	// 这里先查询所有模块的配额包记录，然后统计对应的Key
	if err := db.Raw(`
		SELECT
			qpr.module,
			COUNT(DISTINCT qpr.api_key) as key_count,
			COUNT(DISTINCT CASE WHEN ak.status = 'ok' THEN qpr.api_key END) as active_count,
			AVG(CASE WHEN qpr.quota > 0 THEN (qpr.used * 100.0 / qpr.quota) ELSE 0 END) as avg_usage
		FROM quota_package_records qpr
		LEFT JOIN api_keys ak ON qpr.api_key = ak.api_key
		WHERE qpr.api_key IS NOT NULL AND qpr.api_key != '' AND qpr.status = 'active'
		GROUP BY qpr.module
	`).Scan(&moduleStatsResults).Error; err != nil {
		logger.Error("Failed to get module statistics", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 转换为响应格式
	moduleStats := make([]ModuleStat, 0, len(moduleStatsResults))
	moduleNameMap := map[string]string{
		"llm": "LLM服务",
		"tts": "TTS服务",
		"asr": "ASR服务",
	}

	for _, result := range moduleStatsResults {
		name := moduleNameMap[result.Module]
		if name == "" {
			name = result.Module
		}

		moduleStats = append(moduleStats, ModuleStat{
			Module:      result.Module,
			Name:        name,
			KeyCount:    result.KeyCount,
			ActiveCount: result.ActiveCount,
			AvgUsage:    result.AvgUsage,
		})
	}

	// 构建响应
	response := KeyOverviewStats{
		TotalKeys:       totalKeys,
		ActiveKeys:      activeKeys,
		BlockedKeys:     blockedKeys,
		KeysWithQuota:   keysWithQuota,
		KeysWithBalance: keysWithBalance,
		TotalQuotaUsage: totalQuotaUsage,
		AvgQuotaUsage:   quotaUsage.AvgUsage,
		ModuleStats:     moduleStats,
	}

	c.JSON(http.StatusOK, response)
}
